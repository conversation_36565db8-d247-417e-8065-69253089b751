﻿﻿using System;

namespace TeyaUIModels.Model
{
    public class CosigningComment : IModel
    {
        public Guid Id { get; set; }
        public Guid CommenterId { get; set; }
        public string CommenterName { get; set; }
        public string Comment { get; set; }
        public DateTime CommentDate { get; set; }
        public bool IsResolved { get; set; } = false;
        public DateTime? ResolvedDate { get; set; }
        public Guid? ResolvedById { get; set; }
        public string ResolvedByName { get; set; }
        public string CommentType { get; set; } = "Review"; // "Review", "Response", "General"
    }
}
